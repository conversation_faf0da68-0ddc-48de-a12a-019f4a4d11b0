{"analysis_metadata": {"analyzed_at": "2024-08-01T00:00:00Z", "project_path": "/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-baidu-translate", "analysis_version": "1.0"}, "project_info": {"project_overview": {"name": "mcp-baidu-translate", "description": "基于百度翻译API的多语言文本翻译服务，支持30多种语言互译", "version": "1.0.6", "platforms": ["all"], "tool_count": 2, "category": "文本处理", "complexity_level": "简单"}, "setup_requirements": {"environment_variables": [{"name": "BAIDU_TRANSLATE_APP_ID", "description": "百度翻译API的APP ID", "required": true}, {"name": "BAIDU_TRANSLATE_APP_KEY", "description": "百度翻译API密钥", "required": true}], "dependencies": ["@modelcontextprotocol/sdk", "node-fetch", "crypto-js"], "installation_steps": ["npm install @mcpcn/mcp-baidu-translate", "在百度翻译开放平台申请API密钥", "设置环境变量BAIDU_TRANSLATE_APP_ID和BAIDU_TRANSLATE_APP_KEY", "配置MCP客户端"]}, "usage_guide": {"sample_prompts": ["帮我把这段英文翻译成中文：Hello World, how are you?", "将这个中文句子翻译成日语：你好世界，今天天气真好", "自动检测语言并翻译成英文：Bonjour le monde", "查看百度翻译支持哪些语言", "把这段代码注释翻译成中文：This function handles user authentication"], "mcp_config": {"mcpServers": {"mcp-baidu-translate": {"command": "npx", "args": ["@mcpcn/mcp-baidu-translate"], "env": {"BAIDU_TRANSLATE_APP_ID": "your_app_id_here", "BAIDU_TRANSLATE_APP_KEY": "your_app_key_here"}}}}, "use_cases": ["多语言文档翻译和本地化", "国际化软件开发中的文本翻译", "跨语言沟通和邮件翻译", "学习外语时的实时翻译助手", "技术文档的多语言版本制作"]}}, "tools": [{"tool_name_cn": "文本翻译", "tool_name_en": "translate_text", "description": "使用百度翻译API将文本从一种语言翻译成另一种语言，支持自动语言检测", "file_capabilities": {"supported_extensions": [], "supports_directory": false, "batch_processing": {"same_type_files": false, "mixed_type_files": false}}, "technical_specs": {"keywords": ["翻译", "百度API", "多语言", "文本处理", "语言检测"], "dependencies": [], "platforms": ["mac", "windows", "linux"], "requires_api_key": true, "external_services": ["baidu_translate_api"]}, "execution_profile": {"direct_executable": true, "dangerous_operation": false, "standalone_display": true, "requires_confirmation": false}, "input_schema": {"type": "object", "required": ["text", "from_lang", "to_lang"], "properties": {"text": {"type": "string", "description": "需要翻译的文本内容"}, "from_lang": {"type": "string", "description": "源语言代码，例如：'en'表示英语，'zh'表示中文，留空则自动检测"}, "to_lang": {"type": "string", "description": "目标语言代码，例如：'zh'表示中文，'en'表示英语"}}}, "output_schema": {"type": "object", "properties": {"content": {"type": "array", "description": "翻译结果"}, "detected_language": {"type": "string", "description": "检测到的源语言（如果源语言未指定）"}, "isError": {"type": "boolean", "description": "是否发生错误"}, "errorMessage": {"type": "string", "description": "错误信息，如果isError为true则提供"}}}, "quality_assessment": {"should_exclude": false, "exclusion_reason": "", "parameter_completeness": "complete", "error_handling": "robust"}}, {"tool_name_cn": "获取支持语言列表", "tool_name_en": "get_supported_languages", "description": "查看百度翻译API支持的所有语言及其对应的语言代码", "file_capabilities": {"supported_extensions": [], "supports_directory": false, "batch_processing": {"same_type_files": false, "mixed_type_files": false}}, "technical_specs": {"keywords": ["语言列表", "语言代码", "百度翻译", "支持语言"], "dependencies": [], "platforms": ["mac", "windows", "linux"], "requires_api_key": false, "external_services": []}, "execution_profile": {"direct_executable": true, "dangerous_operation": false, "standalone_display": true, "requires_confirmation": false}, "input_schema": {"type": "object", "properties": {}, "required": []}, "output_schema": {"type": "object", "properties": {"content": {"type": "array", "description": "语言列表"}, "isError": {"type": "boolean", "description": "是否发生错误"}, "errorMessage": {"type": "string", "description": "错误信息，如果isError为true则提供"}}}, "quality_assessment": {"should_exclude": false, "exclusion_reason": "", "parameter_completeness": "complete", "error_handling": "robust"}}], "recommendations": {"integration_suggestions": ["可与文档处理工具组合，实现文档的批量翻译", "结合OCR工具，可实现图片文字的翻译功能", "与文件管理工具配合，可批量处理多语言文件"], "improvement_areas": ["可增加批量文本翻译功能", "可添加翻译质量评估", "可支持更多翻译引擎的切换"], "user_experience_notes": ["工具使用简单，适合各种技术水平的用户", "需要申请百度API密钥，有一定的设置门槛", "翻译质量依赖百度翻译API的准确性"]}}